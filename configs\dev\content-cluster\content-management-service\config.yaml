# 内容管理服务配置文件
server:
  port: 12002

# Redis配置
redis:
  db: 1

# JWT配置
jwt:
  secret: "your-secret-key"
  expire_hours: 168
  issuer: "pxpat-content-intra"

# 日志配置
log:
  file_path: "logs/content-intra.log"
  max_size: 100
  max_backups: 3
  max_age: 28
  compress: true

# 内容配置
content:
  # 内容分级
  rating:
    enabled: true
    levels: ["A", "B", "C"]
    default: "B"
  
  # 地区控制
  region:
    enabled: true
    default_regions: ["CN", "US", "JP"]
    restricted_regions: []
  
  # 内容限制
  limits:
    title_max_length: 200
    description_max_length: 2000
    tags_max_count: 10
    categories_max_count: 5
  
  # 缓存配置
  cache:
    content_ttl: 30m
    category_ttl: 1h
    tag_ttl: 1h
    rating_ttl: 2h

# 外部服务配置
external_services:
  # 用户服务
  user_service:
    host: "localhost"
    port: 11001
    timeout: 10s
    retry_count: 3
  
  # 存储服务
  storage_service:
    host: "localhost"
    port: 13001
    timeout: 30s
    retry_count: 3
  
  # 审核服务
  audit_service:
    host: "localhost"
    port: 17002
    timeout: 5s
    retry_count: 2

# 监控配置
monitoring:
  metrics:
    enabled: true
    path: "/metrics"
  
  health:
    enabled: true
    path: "/health"
  
  pprof:
    enabled: false
    path: "/debug/pprof"
