server:
  port: 10001
  name: "admin-intra"
  version: "1.0.0"
  
database:
  type: postgres
  host: postgres
  port: 5432
  user: postgres
  password: postgres
  dbname: pxpat  # 与user-service共用数据库
  sslmode: disable
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime_minutes: 60

jwt:
  secret: "admin-jwt-secret-key"
  expiration: 2h

api:
  user_service_url: "user-service:11001"

logging:
  level: "debug"
  format: "console" 