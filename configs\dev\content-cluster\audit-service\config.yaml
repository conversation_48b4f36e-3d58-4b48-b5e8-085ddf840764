# 审核服务配置文件
server:
  port: 16001

redis:
  db: 3

# Consul服务发现配置
consul:
  enabled: true
  address: "127.0.0.1:8500"
  datacenter: "dc1"
  service_name: "audit-service"
  service_id: "audit-service-1"
  service_address: "localhost"
  service_port: 16001
  health_check_path: "/health"
  check_interval: "15s"
  check_timeout: "5s"
  deregister_after: "30s"
  tags:
    - "content-cluster"
    - "audit"
    - "v1.0.0"

# 日志配置
log:
  max_size: 100
  max_backups: 3
  max_age: 28
  compress: true

audit:
  # 基础配置
  required_votes: 10        # 默认需要投票数
  task_timeout: "24h"       # 任务超时时间
  assign_timeout: "2h"      # 分配超时时间
  max_daily_tasks: 50       # 每日最大任务数
  
  # 投票权重配置
  base_vote_weight: 1.0     # 基础投票权重
  level_multiplier: 0.1     # 等级权重倍数
  reputation_factor: 0.01   # 信誉权重因子
  
  # 奖励配置
  base_reward: 1.0          # 基础奖励(PAT)
  quality_bonus: 0.5        # 质量奖励
  speed_bonus: 0.2          # 速度奖励
  
  # 自动审核配置
  enable_auto_audit: true   # 启用自动审核
  auto_pass_score: 0.9      # 自动通过分数
  auto_reject_score: 0.1    # 自动拒绝分数
