# 帮助中心服务配置文件
server:
  port: "12004"

# Consul服务发现配置
consul:
  enabled: true
  address: "127.0.0.1:8500"
  datacenter: "dc1"
  service_name: "help-service"
  service_id: "help-service-1"
  service_address: "localhost"
  service_port: 12004
  health_check_path: "/health"
  check_interval: "15s"
  check_timeout: "5s"
  deregister_after: "30s"
  tags:
    - "content-cluster"
    - "help"
    - "v1.0.0"

otlp:
  service_name: "help-service"
  service_version: "1.0.0"
  # 链路追踪配置
  tracing:
    enabled: true
    service_name: "help-service"
    service_version: "1.0.0"
    exporter_type: "otlp-http"  # 使用HTTP方式发送到OTLP收集器
    endpoint: "localhost:4318"  # OTLP HTTP端点（4318是HTTP端口，4317是gRPC端口）
    sampling_ratio: 1.0  # 开发环境100%采样
    timeout: 10s
    insecure: true
  # 指标采集配置
  metrics:
    enabled: true
    service_name: "help-service"
    service_version: "1.0.0"
    endpoint: "localhost:4318"  # OTLP HTTP端点（4318是HTTP端口，4317是gRPC端口）
    exporter_type: "otlp-http"
    interval: 30s
    timeout: 10s
    insecure: true

# 安全配置
security:

