# 小说服务配置文件
server:
  port: 12002
  service_name: "novel-service"

# Consul服务发现配置
consul:
  enabled: true
  address: "127.0.0.1:8500"
  datacenter: "dc1"
  service_name: "novel-service"
  service_id: "novel-service-1"
  service_address: "localhost"
  service_port: 12002
  health_check_path: "/health"
  check_interval: "15s"
  check_timeout: "5s"
  deregister_after: "30s"
  tags:
    - "content-cluster"
    - "novel"
    - "v1.0.0"

# Redis配置
redis:
  db: 2

# 日志配置
log:
  max_size: 100
  max_backups: 3
  max_age: 28
  compress: true

# 小说配置
novel:
  # 小说分级
  rating:
    enabled: true
    levels: ["general", "teen", "mature"]
    default: "general"
  
  # 内容限制
  content:
    title_max_length: 200
    description_max_length: 2000
    tags_max_count: 10
    categories_max_count: 5
    author_max_length: 100
  
  # 章节配置
  chapter:
    title_max_length: 200
    content_max_length: 50000
    summary_max_length: 500
    min_word_count: 100
    max_word_count: 20000
    
    # VIP章节配置
    vip_enabled: true
    min_vip_price: 10      # 最低1分
    max_vip_price: 1000    # 最高10元
    default_vip_price: 100 # 默认1元
  
  # 评论配置
  comment:
    content_max_length: 2000
    quote_max_length: 500
    rating_enabled: true
    spoiler_enabled: true
    report_enabled: true
  
  # 打赏配置
  donation:
    enabled: true
    min_amount: 100        # 最小1元
    max_amount: 100000     # 最大1000元
    platform_fee_rate: 0.1 # 平台手续费10%
    
    # 排行榜配置
    rank_enabled: true
    rank_update_cron: "@hourly"  # 每小时更新
    rank_display_count: 100      # 显示前100名
  
  # 缓存配置
  cache:
    novel_ttl: 30m
    chapter_ttl: 15m
    category_ttl: 1h
    tag_ttl: 1h
    comment_ttl: 10m
    donation_ttl: 5m
    rank_ttl: 30m
    read_record_ttl: 24h

# 监控配置
monitoring:
  metrics:
    enabled: true
    path: "/metrics"

  health:
    enabled: true
    path: "/health"

  pprof:
    enabled: false
    path: "/debug/pprof"

# 存储配置
storage:
  minio:
    provider: "minio"
    bucket: "novel-storage"
    url_expiry: 24h  # URL过期时间
  
  # 封面配置
  cover:
    max_size: 10485760    # 10MB
    allowed_types: ["jpeg", "jpg", "png"]
    quality: 85
    max_width: 1920
    max_height: 1080

# 支付配置
payment:
  enabled: true
  default_provider: "alipay"
  currency: "CNY"
  
  providers:
    alipay:
      enabled: true
      app_id: "your_alipay_app_id"
      app_secret: "your_alipay_app_secret"
      merchant_id: "your_alipay_merchant_id"
      notify_url: "http://your-domain.com/api/v1/payment/notify/alipay"
      return_url: "http://your-domain.com/payment/return"
    
    wechat:
      enabled: true
      app_id: "your_wechat_app_id"
      app_secret: "your_wechat_app_secret"
      merchant_id: "your_wechat_merchant_id"
      notify_url: "http://your-domain.com/api/v1/payment/notify/wechat"
      return_url: "http://your-domain.com/payment/return"

# 安全配置
security:
  cors:
    allowed_origins: "*"
    allowed_methods: "GET, POST, PUT, DELETE, OPTIONS"
    allowed_headers: "*"
    exposed_headers: "Content-Length"
    allow_credentials: "true"
    max_age: 12

  rate_limit:
    enabled: true
    requests_per_minute: 100
    burst: 200
