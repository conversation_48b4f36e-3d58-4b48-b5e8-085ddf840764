# 合集服务配置文件
server:
  port: 12009

redis:
  db: 4

# Consul服务发现配置
consul:
  enabled: true
  address: "127.0.0.1:8500"
  datacenter: "dc1"
  service_name: "interaction-service"
  service_id: "interaction-service-1"
  service_address: "localhost"
  service_port: 17001
  health_check_path: "/health"
  check_interval: "15s"
  check_timeout: "5s"
  deregister_after: "30s"
  tags:
    - "user-cluster"
    - "album"
    - "v1.0.0"

# 日志配置
log:
  max_size: 100
  max_backups: 3
  max_age: 28
  compress: true

# OpenTelemetry配置
otlp:
  service_name: "interaction-service"
  service_version: "1.0.0"
  # 链路追踪配置
  tracing:
    enabled: true
    service_name: "interaction-service"
    service_version: "1.0.0"
    exporter_type: "otlp-http"  # 使用HTTP方式发送到OTLP收集器
    endpoint: "localhost:4318"  # OTLP HTTP端点（4318是HTTP端口，4317是gRPC端口）
    sampling_ratio: 1.0  # 开发环境100%采样
    timeout: 10s
    insecure: true
  # 指标采集配置
  metrics:
    enabled: true
    service_name: "interaction-service"
    service_version: "1.0.0"
    endpoint: "localhost:4318"  # OTLP HTTP端点（4318是HTTP端口，4317是gRPC端口）
    exporter_type: "otlp-http"
    interval: 30s
    timeout: 10s
    insecure: true

# 安全配置
security:
  cors:
    allow_origins: ["*"]
    allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allow_headers: ["*"]
    expose_headers: ["*"]
    allow_credentials: true
    max_age: 12

# 合集配置
album:
  # 基础配置
  max_albums_per_user: 100        # 每个用户最多创建的合集数
  max_contents_per_album: 1000    # 每个合集最多包含的内容数
  
  # 名称和描述限制
  max_name_length: 100            # 合集名称最大长度
  max_description_length: 500     # 合集描述最大长度
  
  # 支持的合集类型
  supported_types:
    - "video"
    - "short_video"
    - "anime"
    - "novel"
